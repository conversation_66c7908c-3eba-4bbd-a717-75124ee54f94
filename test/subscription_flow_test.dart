import 'package:flutter_test/flutter_test.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription_plan_feature.dart';
import 'package:techrar_gym/app/subscriptions/models/purchase_subscription_response.dart';
import 'package:techrar_gym/core/api/api_response.dart';

void main() {
  group('Subscription Flow Tests', () {
    test('SubscriptionPlan model should parse from JSON correctly', () {
      final json = {
        'id': 'plan_123',
        'name': 'Premium Plan',
        'name_alt': 'Premium Membership',
        'description': 'Full access to all facilities',
        'price': 99.99,
        'duration': 30,
        'classes_per_week': 5,
        'features': [
          {
            'name': 'gym_access',
            'name_alt': 'Gym Access',
            'value': 'Unlimited',
            'isNumeric': false,
            'isPremium': true,
            'icon': 'fitness_center'
          }
        ],
        'freeze_days_credit': 7,
        'freeze_attempts_credit': 2,
        'is_active': true,
        'created_at': '2024-01-01T00:00:00Z',
      };

      final plan = SubscriptionPlan.fromJson(json);

      expect(plan.id, 'plan_123');
      expect(plan.name, 'Premium Plan');
      expect(plan.nameAlt, 'Premium Membership');
      expect(plan.description, 'Full access to all facilities');
      expect(plan.price, 99.99);
      expect(plan.duration, 30);
      expect(plan.classesPerWeek, 5);
      expect(plan.freezeDaysCredit, 7);
      expect(plan.freezeAttemptsCredit, 2);
      expect(plan.isActive, true);
      expect(plan.features.length, 1);
      expect(plan.features.first.name, 'gym_access');
    });

    test('SubscriptionPlan should format duration correctly', () {
      final planDaily = SubscriptionPlan(
        id: '1',
        name: 'Daily',
        price: 10.0,
        duration: 1,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planWeekly = SubscriptionPlan(
        id: '2',
        name: 'Weekly',
        price: 50.0,
        duration: 7,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planMonthly = SubscriptionPlan(
        id: '3',
        name: 'Monthly',
        price: 100.0,
        duration: 30,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      final planYearly = SubscriptionPlan(
        id: '4',
        name: 'Yearly',
        price: 1000.0,
        duration: 365,
        features: [],
        freezeDaysCredit: 0,
        freezeAttemptsCredit: 0,
        isActive: true,
        createdAt: DateTime.now(),
      );

      expect(planDaily.durationText, '1 Day');
      expect(planWeekly.durationText, '1 Week');
      expect(planMonthly.durationText, 'Monthly subscription');
      expect(planYearly.durationText, '1 Year');
    });

    test('SubscriptionPlanFeature should parse correctly', () {
      final json = {
        'name': 'gym_access',
        'name_alt': 'Gym Access',
        'value': 'Unlimited',
        'isNumeric': false,
        'isPremium': true,
        'icon': 'fitness_center'
      };

      final feature = SubscriptionPlanFeature.fromJson(json);

      expect(feature.name, 'gym_access');
      expect(feature.nameAlt, 'Gym Access');
      expect(feature.value, 'Unlimited');
      expect(feature.isNumeric, false);
      expect(feature.isPremium, true);
      expect(feature.icon, 'fitness_center');
      expect(feature.displayText, 'Gym Access');
      expect(feature.isHighlighted, true);
    });

    test('PurchaseSubscriptionResponse should parse correctly with new API structure', () {
      final json = {
        'success': true,
        'message': 'Subscription purchased successfully!',
        'data': {
          'subscription': {
            'id': 'sub_123',
            'member_id': 'member_123',
            'plan_id': 'plan_123',
            'status': 'active',
            'start_date': '2024-01-01T00:00:00Z',
            'end_date': '2024-02-01T00:00:00Z',
            'auto_renew': true,
            'created_at': '2024-01-01T00:00:00Z',
            'plan': {
              'id': 'plan_123',
              'name': 'Monthly Plan',
              'description': 'Monthly subscription',
              'price': 399.0,
              'duration': 30,
              'features': [],
              'freeze_days_credit': 0,
              'freeze_attempts_credit': 0,
              'is_active': true,
              'created_at': '2024-01-01T00:00:00Z',
            },
          },
          'payment': {
            'amount': 399,
            'currency': 'SAR',
            'payment_method': 'credit_card',
            'status': 'completed',
            'transaction_id': 'mock_txn_1750077116151',
            'processed_at': '2025-06-16T12:31:56.151Z'
          },
          'welcome_message': 'Welcome to Monthly Xpress! Your subscription is now active and will expire on 7/16/2025.'
        },
        'errors': null
      };

      final response = ApiResponse.fromJson(
        json,
        (data) => PurchaseSubscriptionData.fromJson(data),
      );

      expect(response.success, true);
      expect(response.message, 'Subscription purchased successfully!');
      expect(response.data, isNotNull);

      final data = response.data!;
      expect(data.subscription.id, 'sub_123');
      expect(data.payment.amount, 399);
      expect(data.payment.currency, 'SAR');
      expect(data.payment.status, 'completed');
      expect(data.payment.isCompleted, true);
      expect(data.payment.formattedAmount, '399.0 SAR');
      expect(data.welcomeMessage,
          'Welcome to Monthly Xpress! Your subscription is now active and will expire on 7/16/2025.');
    });

    test('PaymentDetails should handle different payment statuses', () {
      final completedPayment = PaymentDetails(
        amount: 100.0,
        currency: 'SAR',
        paymentMethod: 'credit_card',
        status: 'completed',
      );

      final pendingPayment = PaymentDetails(
        amount: 100.0,
        currency: 'SAR',
        paymentMethod: 'credit_card',
        status: 'pending',
      );

      final failedPayment = PaymentDetails(
        amount: 100.0,
        currency: 'SAR',
        paymentMethod: 'credit_card',
        status: 'failed',
      );

      expect(completedPayment.isCompleted, true);
      expect(completedPayment.isPending, false);
      expect(completedPayment.isFailed, false);

      expect(pendingPayment.isCompleted, false);
      expect(pendingPayment.isPending, true);
      expect(pendingPayment.isFailed, false);

      expect(failedPayment.isCompleted, false);
      expect(failedPayment.isPending, false);
      expect(failedPayment.isFailed, true);
    });
  });
}
