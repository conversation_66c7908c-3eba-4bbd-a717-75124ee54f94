import 'dart:convert';
import 'dart:io';
import 'package:fpdart/fpdart.dart';
import 'package:http/http.dart' as http;
import 'api_response.dart';
import 'api_constants.dart';
import 'api_exceptions.dart';
import '../services/storage_service.dart';
import '../../app/auth/models/refresh_token_request.dart';
import '../../app/auth/models/refresh_token_response.dart';

typedef FutureEither<T> = Future<Either<String, T>>;

class ApiService {
  static const bool _isProduction = false; // Set to false for development
  static String get baseUrl => _isProduction ? ApiConstants.baseUrl : ApiConstants.devBaseUrl;

  static bool _isRefreshing = false;
  static List<Function> _refreshQueue = [];

  static Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {
      'Content-Type': ApiConstants.contentType,
      'Merchant-ID': ApiConstants.merchantId,
    };

    if (includeAuth) {
      final token = await _getValidAccessToken();
      if (token != null) {
        headers['Authorization'] = '${ApiConstants.bearer} $token';
      }
    }

    return headers;
  }

  /// Get a valid access token, refreshing if necessary
  static Future<String?> _getValidAccessToken() async {
    final token = await StorageService.getAccessToken();
    final isExpired = await StorageService.isTokenExpired();

    if (token == null) return null;

    if (!isExpired) return token;

    // Token is expired, try to refresh
    final refreshed = await _refreshToken();
    if (refreshed) {
      return await StorageService.getAccessToken();
    }

    return null;
  }

  /// Refresh the access token using the refresh token
  static Future<bool> _refreshToken() async {
    if (_isRefreshing) {
      // Wait for ongoing refresh to complete
      return await _waitForRefresh();
    }

    _isRefreshing = true;

    try {
      final refreshToken = await StorageService.getRefreshToken();
      if (refreshToken == null) {
        _isRefreshing = false;
        return false;
      }

      final request = RefreshTokenRequest(refreshToken: refreshToken);

      final headers = {
        'Content-Type': ApiConstants.contentType,
        'Merchant-ID': ApiConstants.merchantId,
      };

      final uri = Uri.parse('$baseUrl${ApiConstants.refresh}');
      final response = await http
          .post(
            uri,
            headers: headers,
            body: jsonEncode(request.toJson()),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final apiResponse = ApiResponse.fromJson(
          jsonResponse,
          (data) => RefreshTokenResponse.fromJson(data),
        );

        if (apiResponse.success && apiResponse.data != null) {
          await StorageService.saveTokens(apiResponse.data!.session);
          _isRefreshing = false;
          _processRefreshQueue(true);
          return true;
        }
      }

      _isRefreshing = false;
      _processRefreshQueue(false);
      return false;
    } catch (e) {
      _isRefreshing = false;
      _processRefreshQueue(false);
      return false;
    }
  }

  /// Wait for ongoing refresh to complete
  static Future<bool> _waitForRefresh() async {
    while (_isRefreshing) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    // Check if token is now valid
    final token = await StorageService.getAccessToken();
    final isExpired = await StorageService.isTokenExpired();
    return token != null && !isExpired;
  }

  /// Process queued requests after refresh attempt
  static void _processRefreshQueue(bool success) {
    for (final callback in _refreshQueue) {
      callback(success);
    }
    _refreshQueue.clear();
  }

  static Future<ApiResponse<T>> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJsonT,
  ) async {
    try {
      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.fromJson(jsonResponse, fromJsonT);
      } else {
        // Handle different error status codes
        switch (response.statusCode) {
          case 400:
            if (jsonResponse['errors'] != null) {
              final errors = (jsonResponse['errors'] as List).map((e) => ValidationError.fromJson(e)).toList();
              throw ValidationException(
                jsonResponse['message'] ?? 'Validation failed',
                errors,
              );
            }
            throw ApiException(
              jsonResponse['message'] ?? 'Bad request',
              statusCode: response.statusCode,
              data: jsonResponse,
            );
          case 401:
            // Try to refresh token if this is an auth failure
            final refreshed = await _refreshToken();
            if (!refreshed) {
              // Refresh failed, clear storage and force logout
              await StorageService.clearAll();
            }
            throw AuthenticationException(
              jsonResponse['message'] ?? 'Authentication failed',
            );
          case 403:
            throw ApiException(
              jsonResponse['message'] ?? 'Access forbidden',
              statusCode: response.statusCode,
            );
          case 404:
            throw ApiException(
              jsonResponse['message'] ?? 'Resource not found',
              statusCode: response.statusCode,
            );
          case 500:
            throw ApiException(
              'Server error. Please try again later.',
              statusCode: response.statusCode,
            );
          default:
            throw ApiException(
              jsonResponse['message'] ?? 'An error occurred',
              statusCode: response.statusCode,
              data: jsonResponse,
            );
        }
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Failed to parse response: $e');
    }
  }

  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http.get(finalUri, headers: headers).timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http
          .post(
            finalUri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http
          .put(
            finalUri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, String>? queryParams,
    bool includeAuth = true,
    T Function(dynamic)? fromJsonT,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      final headers = await _getHeaders(includeAuth: includeAuth);

      final response = await http.delete(finalUri, headers: headers).timeout(const Duration(seconds: 30));

      return _handleResponse(response, fromJsonT);
    } on SocketException {
      throw NetworkException('No internet connection');
    } on HttpException {
      throw NetworkException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }
}
