import 'package:flutter/material.dart';
import 'package:techrar_gym/core/services/extension.dart';

import '../services/custom_font_loader.dart';
import 'app_colors.dart';

class Insets {
  static const double xs = 2;

  static const double s = 6;

  static const double m = 12;

  static const double l = 20;

  static const double xl = 24;

  static const double xxl = 36;
}

class Sizes {
  static const double xxlCardHeight = 170.0;

  static const double xlCardHeight = 120.0;

  static const double lCardHeight = 80.0;

  static const double mCardHeight = 60.0;

  static const double sCardHeight = 45.0;

  static const double xsCardHeight = 38.0;

  static const double xxsCardHeight = 30.0;

  static const double mIcon = 20;

  static const double lIcon = 30;

  static const double xlIcon = 40;
}

class TextStyles {
  static CustomFontLoader fontLoader = CustomFontLoader();

  static TextStyle get varelaRound => TextStyle(
        fontFamily: "VarelaRound",
        fontWeight: FontWeight.normal,
        color: AppColors.textColor,
        height: 1.1,
        fontFamilyFallback: const [
          'Tajawal',
          'Almarai',
        ],
      );

  static TextStyle get currentFont {
    final String lang = 'en'; // Default to English, can be made dynamic later
    return TextStyle(
      fontFamily: fontLoader.getFontFamily(lang),
      color: AppColors.textColor,
      height: 1.1,
      fontFamilyFallback: const [
        'Almarai',
        'VarelaRound',
        'Tajawal',
      ],
    );
  }

  ///fontWeight: FontWeight.bold, fontSize: 32
  static TextStyle get logo => varelaRound.copyWith(fontWeight: FontWeight.bold, fontSize: 32);

  ///fontWeight: FontWeight.bold, fontSize: 26
  static TextStyle get t1 => currentFont.copyWith(fontWeight: FontWeight.bold, fontSize: 26);

  ///fontWeight: FontWeight.bold, fontSize: 22
  static TextStyle get t2 => currentFont.copyWith(fontWeight: FontWeight.bold, fontSize: 22);

  ///fontSize: 20
  static TextStyle get h1 => currentFont.copyWith(fontSize: 20);

  ///fontSize: 20, fontWeight: FontWeight.bold
  static TextStyle get h1b => currentFont.copyWith(fontSize: 20, fontWeight: FontWeight.bold);

  ///fontSize: 18
  static TextStyle get h2 => currentFont.copyWith(fontSize: 18);

  ///fontSize: 18, fontWeight: FontWeight.bold
  static TextStyle get h2b => currentFont.copyWith(fontSize: 18, fontWeight: FontWeight.bold);

  ///fontSize: 16
  static TextStyle get body1 => currentFont.copyWith(fontSize: 16);

  ///fontSize: 16, fontWeight: FontWeight.bold
  static TextStyle get body1b => currentFont.copyWith(fontSize: 16, fontWeight: FontWeight.bold);

  ///fontSize: 14
  static TextStyle get body2 => currentFont.copyWith(fontSize: 14);

  ///fontSize: 14, fontWeight: FontWeight.bold
  static TextStyle get body2b => currentFont.copyWith(fontSize: 14, fontWeight: FontWeight.bold);

  ///fontSize: 13
  static TextStyle get body3 => currentFont.copyWith(fontSize: 13);

  ///fontSize: 13, fontWeight: FontWeight.bold
  static TextStyle get body3b => currentFont.copyWith(fontSize: 13, fontWeight: FontWeight.bold);

  ///fontSize: 16
  static TextStyle get callOut => currentFont.copyWith(fontSize: 16);

  ///fontSize: 16, fontWeight: FontWeight.bold
  static TextStyle get callOutFocus => callOut.copyWith(fontWeight: FontWeight.bold);

  ///fontSize: 16
  static TextStyle get button => callOut;

  ///fontSize: 16
  static TextStyle get buttonSelected => button.copyWith(fontWeight: FontWeight.normal);

  ///fontSize: 13
  static TextStyle get footnote => body3;

  ///fontSize: 13, color: AppColors.grey
  static TextStyle get hint => body3.withColor(AppColors.grey);

  ///fontSize: 11, color: AppColors.grey
  static TextStyle get hint2 => hint.withSize(11);

  ///fontSize: 10, color: AppColors.grey
  static TextStyle get hint3 => hint.withSize(10);

  ///fontSize: 13
  static TextStyle get caption => footnote;

  ///fontSize: 13, decoration: TextDecoration.underline, color: AppColors.infoColor
  static TextStyle get clickable => footnote.copyWith(decoration: TextDecoration.underline, color: AppColors.infoColor);

  // Additional Text Styles for UI Components
  static TextStyle get buttonText => body1.copyWith(fontWeight: FontWeight.w600, color: AppColors.buttonTextColor);
  static TextStyle get buttonSecondaryText =>
      body1.copyWith(fontWeight: FontWeight.w600, color: AppColors.buttonSecondaryTextColor);
  static TextStyle get inputLabel => body2.copyWith(fontWeight: FontWeight.w600, color: AppColors.primaryTextColor);
  static TextStyle get inputText => body1.copyWith(color: AppColors.primaryTextColor);
  static TextStyle get inputHint => body1.copyWith(color: AppColors.hintTextColor);
  static TextStyle get errorText => caption.copyWith(color: AppColors.errorColor);
  static TextStyle get successText => caption.copyWith(color: AppColors.successColor);
  static TextStyle get warningText => caption.copyWith(color: AppColors.warningColor);
  static TextStyle get infoText => caption.copyWith(color: AppColors.infoColor);
}

// Spacing Constants
class AppSpacing {
  // Base spacing unit (8px)
  static const double unit = 8.0;

  // Micro spacing
  static const double xs = unit * 0.5; // 4px
  static const double sm = unit; // 8px

  // Standard spacing
  static const double md = unit * 2; // 16px
  static const double lg = unit * 3; // 24px
  static const double xl = unit * 4; // 32px
  static const double xxl = unit * 6; // 48px
  static const double xxxl = unit * 8; // 64px

  // Component specific spacing
  static const double cardPadding = md;
  static const double screenPadding = lg;
  static const double sectionSpacing = xl;
  static const double elementSpacing = md;
  static const double buttonSpacing = sm;

  // Form spacing
  static const double formFieldSpacing = md;
  static const double formSectionSpacing = xl;
  static const double formPadding = lg;
}

// Size Constants
class AppSizes {
  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 40.0;
  static const double iconXxl = 48.0;

  // Button sizes
  static const double buttonHeight = 56.0;
  static const double buttonHeightSm = 40.0;
  static const double buttonHeightLg = 64.0;
  static const double buttonRadius = 8.0;
  static const double buttonRadiusSm = 8.0;
  static const double buttonRadiusLg = 16.0;

  // Input sizes
  static const double inputHeight = 56.0;
  static const double inputRadius = 12.0;
  static const double inputBorderWidth = 1.0;
  static const double inputFocusedBorderWidth = 2.0;

  // Card sizes
  static const double cardRadius = 8.0;
  static const double cardRadiusSm = 8.0;
  static const double cardRadiusLg = 20.0;
  static const double cardElevation = 4.0;

  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 48.0;
  static const double avatarLg = 64.0;
  static const double avatarXl = 80.0;

  // App bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;

  // Bottom navigation
  static const double bottomNavHeight = 80.0;

  // Divider
  static const double dividerThickness = 1.0;

  // Border radius
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 20.0;
  static const double radiusXxl = 24.0;
  static const double radiusRound = 50.0;
}

// Animation Durations
class AppDurations {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 800);

  // Component specific durations
  static const Duration buttonPress = fast;
  static const Duration pageTransition = normal;
  static const Duration modalTransition = normal;
  static const Duration loadingIndicator = slow;
}

class Borders {
  static final Border blackBorder = Border.all(color: AppColors.textColor, width: 1.5);

  static const BorderRadius xsBorderRadius = BorderRadius.all(Radius.circular(3));
  static const BorderRadius sBorderRadius = BorderRadius.all(Radius.circular(5));
  static const BorderRadius mBorderRadius = BorderRadius.all(Radius.circular(10));
  static const BorderRadius lBorderRadius = BorderRadius.all(Radius.circular(15));
  static const BorderRadius xlBorderRadius = BorderRadius.all(Radius.circular(25));
  static const BorderRadius xxlBorderRadius = BorderRadius.all(Radius.circular(35));
}

class Styles {
  static List<BoxShadow> get boxShadow => [
        BoxShadow(blurRadius: 7, color: AppColors.shadowColor, offset: const Offset(0, 4)),
      ];

  static List<BoxShadow> get unifiedShadow => [
        BoxShadow(blurRadius: 1, color: AppColors.lightShadowColor),
      ];

  /// blurRadius: 3, color: AppColors.shadowColor
  static List<BoxShadow> get unifiedShadow2 => [
        BoxShadow(blurRadius: 5, color: AppColors.lightShadowColor),
      ];

  static List<BoxShadow> get boxShadowTop => [
        BoxShadow(blurRadius: 2, color: AppColors.lightShadowColor, offset: const Offset(0, -1)),
      ];

  static List<BoxShadow> get boxShadowLightBottom => [
        BoxShadow(blurRadius: 2, color: AppColors.lightShadowColor, offset: const Offset(0, 0)),
      ];

  static List<BoxShadow> get boxShadowBottom => [
        BoxShadow(blurRadius: 1, color: AppColors.lightShadowColor, offset: const Offset(0, 1)),
      ];

  static List<BoxShadow> get boxShadowHeavy => [
        BoxShadow(blurRadius: 10, color: AppColors.shadowColor.withValues(alpha: 0.3), offset: const Offset(-2, 2)),
      ];

  static BoxDecoration get containerDecoration =>
      BoxDecoration(boxShadow: unifiedShadow2, borderRadius: Borders.mBorderRadius, color: AppColors.white);

  static List<BoxShadow> get floatingShadow =>
      [BoxShadow(color: AppColors.shadowColor, blurRadius: 20, offset: const Offset(0, 5))];
}
