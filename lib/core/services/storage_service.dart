import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';
import 'dart:convert';

import 'package:techrar_gym/app/auth/models/session.dart';

class StorageService {
  static const _storage = FlutterSecureStorage();

  // Keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiresAtKey = 'token_expires_at';
  static const String _customerKey = 'customer';

  // Token management
  static Future<void> saveTokens(Session session) async {
    await _storage.write(key: _accessTokenKey, value: session.accessToken);
    await _storage.write(key: _refreshTokenKey, value: session.refreshToken);
    await _storage.write(key: _tokenExpiresAtKey, value: session.expiresAt.toString());
  }

  static Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }

  static Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  static Future<bool> isTokenExpired() async {
    final expiresAtStr = await _storage.read(key: _tokenExpiresAtKey);
    if (expiresAtStr == null) return true;

    final expiresAt = int.tryParse(expiresAtStr);
    if (expiresAt == null) return true;

    return DateTime.now().millisecondsSinceEpoch > expiresAt * 1000;
  }

  // Customer management
  static Future<void> saveCustomer(Customer customer) async {
    await _storage.write(key: _customerKey, value: jsonEncode(customer.toJson()));
  }

  static Future<Customer?> getCustomer() async {
    final customerStr = await _storage.read(key: _customerKey);
    if (customerStr == null) return null;

    try {
      final customerJson = jsonDecode(customerStr);
      return Customer.fromJson(customerJson);
    } catch (e) {
      return null;
    }
  }

  // Clear all data
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getAccessToken();
    final isExpired = await isTokenExpired();
    return token != null && !isExpired;
  }

  // Generic string storage methods
  static Future<void> saveString(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  static Future<String?> getString(String key) async {
    return await _storage.read(key: key);
  }
}
