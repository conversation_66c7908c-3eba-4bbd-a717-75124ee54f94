import 'dart:convert';
import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'storage_service.dart';

/// Supported languages
enum SupportedLanguage {
  english('en', 'English'),
  arabic('ar', 'العربية');

  const SupportedLanguage(this.code, this.name);
  final String code;
  final String name;
}

/// Localization service provider
final localizationServiceProvider = Provider<LocalizationService>(
  (ref) => LocalizationService(),
);

/// Current language provider
final currentLanguageProvider = StateNotifierProvider<LanguageNotifier, SupportedLanguage>(
  (ref) => LanguageNotifier(),
);

class LanguageNotifier extends StateNotifier<SupportedLanguage> {
  static const String _languageKey = 'selected_language';

  LanguageNotifier() : super(SupportedLanguage.english) {
    _loadSavedLanguage();
  }

  /// Load saved language or use device default
  Future<void> _loadSavedLanguage() async {
    try {
      final savedLanguage = await StorageService.getString(_languageKey);
      if (savedLanguage != null) {
        final language = SupportedLanguage.values.firstWhere(
          (lang) => lang.code == savedLanguage,
          orElse: () => _getDeviceLanguage(),
        );
        state = language;
      } else {
        state = _getDeviceLanguage();
      }
    } catch (e) {
      state = _getDeviceLanguage();
    }
  }

  /// Get device language or default to English
  SupportedLanguage _getDeviceLanguage() {
    final deviceLocale = PlatformDispatcher.instance.locale.languageCode;
    return SupportedLanguage.values.firstWhere(
      (lang) => lang.code == deviceLocale,
      orElse: () => SupportedLanguage.english,
    );
  }

  /// Change language and save preference
  Future<void> changeLanguage(SupportedLanguage language) async {
    state = language;
    await StorageService.saveString(_languageKey, language.code);
  }
}

class LocalizationService {
  static final Map<String, Map<String, String>> _translations = {};
  static bool _isLoaded = false;

  /// Load translations for all supported languages
  Future<void> loadTranslations() async {
    if (_isLoaded) return;

    try {
      for (final language in SupportedLanguage.values) {
        final translationString = await rootBundle.loadString(
          'assets/translations/${language.code}.json',
        );
        final Map<String, dynamic> translationMap = json.decode(translationString);
        _translations[language.code] = translationMap.cast<String, String>();
      }
      _isLoaded = true;
    } catch (e) {
      // If translations fail to load, continue with empty translations
      // This ensures the app doesn't crash
      _isLoaded = true;
    }
  }

  /// Get translation for a key in the specified language
  String translate(String key, SupportedLanguage language) {
    if (!_isLoaded) return key;
    
    final languageTranslations = _translations[language.code];
    if (languageTranslations == null) return key;
    
    return languageTranslations[key] ?? key;
  }

  /// Get translation with fallback to English if not found in current language
  String translateWithFallback(String key, SupportedLanguage language) {
    if (!_isLoaded) return key;
    
    // Try current language first
    final currentTranslations = _translations[language.code];
    if (currentTranslations != null && currentTranslations.containsKey(key)) {
      return currentTranslations[key]!;
    }
    
    // Fallback to English
    final englishTranslations = _translations[SupportedLanguage.english.code];
    if (englishTranslations != null && englishTranslations.containsKey(key)) {
      return englishTranslations[key]!;
    }
    
    // Return key if no translation found
    return key;
  }

  /// Check if language is RTL
  bool isRTL(SupportedLanguage language) {
    return language == SupportedLanguage.arabic;
  }
}

/// Extension for easy translation access
extension StringTranslation on String {
  String tr(WidgetRef ref) {
    final localizationService = ref.read(localizationServiceProvider);
    final currentLanguage = ref.watch(currentLanguageProvider);
    return localizationService.translateWithFallback(this, currentLanguage);
  }
}

/// Utility function for API response language handling
class ApiLanguageHelper {
  /// Get the appropriate text based on current language
  /// Handles name/name_alt and description/description_alt fields
  static String getLocalizedText({
    required String? mainText,
    required String? altText,
    required SupportedLanguage currentLanguage,
  }) {
    if (currentLanguage == SupportedLanguage.arabic && altText != null && altText.isNotEmpty) {
      return altText;
    }
    return mainText ?? '';
  }

  /// Get localized name from API response
  static String getLocalizedName({
    required String? name,
    required String? nameAlt,
    required SupportedLanguage currentLanguage,
  }) {
    return getLocalizedText(
      mainText: name,
      altText: nameAlt,
      currentLanguage: currentLanguage,
    );
  }

  /// Get localized description from API response
  static String getLocalizedDescription({
    required String? description,
    required String? descriptionAlt,
    required SupportedLanguage currentLanguage,
  }) {
    return getLocalizedText(
      mainText: description,
      altText: descriptionAlt,
      currentLanguage: currentLanguage,
    );
  }
}
