import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:techrar_gym/app/auth/models/auth_response.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';
import 'package:techrar_gym/app/auth/models/refresh_token_request.dart';
import 'package:techrar_gym/app/auth/models/refresh_token_response.dart';
import 'package:techrar_gym/core/api/api_constants.dart';
import 'package:techrar_gym/core/api/api_response.dart';
import 'package:techrar_gym/core/api/api_service.dart';
import 'package:techrar_gym/core/services/storage_service.dart';

final authServiceProvider = Provider<AuthService>(
  (ref) => AuthService(ref),
);

class AuthService {
  final Ref _ref; // use for reading other providers
  AuthService(this._ref);

  FutureEither<ApiResponse<Map<String, dynamic>>> register({
    required String fullName,
    required String email,
    required String password,
    required String phone,
    required String gender,
    required DateTime dateOfBirth,
  }) async {
    final body = {
      'full_name': fullName,
      'email': email,
      'password': password,
      'phone': phone,
      'gender': gender,
      'date_of_birth': dateOfBirth.toIso8601String().split('T')[0],
    };

    try {
      return right(await ApiService.post<Map<String, dynamic>>(
        ApiConstants.register,
        body: body,
        includeAuth: false,
        fromJsonT: (data) => data as Map<String, dynamic>,
      ));
    } catch (e) {
      return left(e.toString());
    }
  }

  Future<Either<String, ApiResponse<AuthResponse>>> login({
    required String email,
    required String password,
  }) async {
    final body = {
      'email': email,
      'password': password,
    };

    try {
      final response = await ApiService.post<AuthResponse>(
        ApiConstants.login,
        body: body,
        includeAuth: false,
        fromJsonT: (data) => AuthResponse.fromJson(data),
      );
      await StorageService.saveTokens(response.data!.session);
      await StorageService.saveCustomer(response.data!.customer);
      return right(response);
    } on Exception catch (e) {
      return left(e.toString());
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> resetPassword({
    required String email,
  }) async {
    final body = {'email': email};

    return await ApiService.post<Map<String, dynamic>>(
      ApiConstants.resetPassword,
      body: body,
      includeAuth: false,
      fromJsonT: (data) => data as Map<String, dynamic>,
    );
  }

  Future<ApiResponse<Customer>> getProfile() async {
    return await ApiService.get<Customer>(
      ApiConstants.profile,
      fromJsonT: (data) => Customer.fromJson(data['customer']),
    );
  }

  Future<ApiResponse<Customer>> updateProfile({
    String? fullName,
    String? phone,
    String? profileImageUrl,
  }) async {
    final body = <String, dynamic>{};

    if (fullName != null) body['full_name'] = fullName;
    if (phone != null) body['phone'] = phone;
    if (profileImageUrl != null) body['profile_image_url'] = profileImageUrl;

    final response = await ApiService.put<Customer>(
      ApiConstants.profile,
      body: body,
      fromJsonT: (data) => Customer.fromJson(data['customer']),
    );

    // Update stored customer data if successful
    if (response.success && response.data != null) {
      await StorageService.saveCustomer(response.data!);
    }

    return response;
  }

  Future<void> logout() async {
    try {
      // Call server logout endpoint to invalidate the token
      await ApiService.post<Map<String, dynamic>>(
        ApiConstants.logout,
        body: {},
        includeAuth: true,
        fromJsonT: (data) => data as Map<String, dynamic>,
      );
    } catch (e) {
      // Even if server logout fails, we should still clear local storage
      // This ensures the user is logged out locally
    } finally {
      // Always clear local storage
      await StorageService.clearAll();
    }
  }

  Future<bool> isLoggedIn() async {
    return await StorageService.isLoggedIn();
  }

  Future<Customer?> getCurrentCustomer() async {
    return await StorageService.getCustomer();
  }

  /// Refresh authentication tokens
  Future<Either<String, ApiResponse<RefreshTokenResponse>>> refreshToken() async {
    try {
      final refreshToken = await StorageService.getRefreshToken();
      if (refreshToken == null) {
        return left('No refresh token available');
      }

      final request = RefreshTokenRequest(refreshToken: refreshToken);

      final response = await ApiService.post<RefreshTokenResponse>(
        ApiConstants.refresh,
        body: request.toJson(),
        includeAuth: false,
        fromJsonT: (data) => RefreshTokenResponse.fromJson(data),
      );

      if (response.success && response.data != null) {
        await StorageService.saveTokens(response.data!.session);
        return right(response);
      } else {
        return left(response.message.isNotEmpty ? response.message : 'Failed to refresh token');
      }
    } catch (e) {
      return left(e.toString());
    }
  }

  /// Check if token needs refresh and refresh if necessary
  Future<bool> ensureValidToken() async {
    final isExpired = await StorageService.isTokenExpired();
    if (!isExpired) return true;

    final result = await refreshToken();
    return result.isRight();
  }
}
