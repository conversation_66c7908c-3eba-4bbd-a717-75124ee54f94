import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/subscriptions/providers/subscription_checkout_provider.dart';
import 'package:techrar_gym/app/subscriptions/widgets/checkout_summary_card.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_confirmation_view.dart';

/// View for subscription checkout
class SubscriptionCheckoutView extends ConsumerStatefulWidget {
  static const String name = 'subscription-checkout-view';

  const SubscriptionCheckoutView({super.key});

  @override
  ConsumerState<SubscriptionCheckoutView> createState() => _SubscriptionCheckoutViewState();
}

class _SubscriptionCheckoutViewState extends ConsumerState<SubscriptionCheckoutView> {
  final _promoCodeController = TextEditingController();

  @override
  void dispose() {
    _promoCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final checkoutState = ref.watch(subscriptionCheckoutProvider);
    final checkoutNotifier = ref.read(subscriptionCheckoutProvider.notifier);

    // If no plan is selected, go back to plans
    if (checkoutState.selectedPlan == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pop();
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.primaryTextColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Checkout',
          style: TextStyles.h2.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(Insets.l),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order summary
            CheckoutSummaryCard(
              plan: checkoutState.selectedPlan!,
              autoRenew: checkoutState.autoRenew,
              promoCode: checkoutState.promoCode,
              totalPrice: checkoutState.totalPrice,
            ),

            const SizedBox(height: Insets.xl),

            // Auto-renewal toggle
            _buildAutoRenewalSection(checkoutState, checkoutNotifier),

            const SizedBox(height: Insets.l),

            // Promo code section
            _buildPromoCodeSection(checkoutState, checkoutNotifier),

            const SizedBox(height: Insets.xl),

            // Error message
            if (checkoutState.error != null)
              Container(
                padding: const EdgeInsets.all(Insets.m),
                margin: const EdgeInsets.only(bottom: Insets.l),
                decoration: BoxDecoration(
                  color: AppColors.errorColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: AppColors.errorColor,
                      size: 20,
                    ),
                    const SizedBox(width: Insets.m),
                    Expanded(
                      child: Text(
                        checkoutState.error!,
                        style: TextStyles.body2.copyWith(
                          color: AppColors.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Subscribe button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: checkoutState.isLoading ? null : () => _handleSubscribe(checkoutNotifier),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: AppColors.buttonTextColor,
                  padding: const EdgeInsets.symmetric(vertical: Insets.l),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                  ),
                  elevation: 0,
                ),
                child: checkoutState.isLoading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.buttonTextColor),
                        ),
                      )
                    : Text(
                        'Subscribe Now',
                        style: TextStyles.buttonText.copyWith(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: Insets.m),

            // Terms and conditions
            Text(
              'By subscribing, you agree to our Terms of Service and Privacy Policy. Your subscription will be charged immediately.',
              style: TextStyles.caption.copyWith(
                color: AppColors.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoRenewalSection(
      SubscriptionCheckoutState checkoutState, SubscriptionCheckoutNotifier checkoutNotifier) {
    return Container(
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
        border: Border.all(
          color: AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Auto-Renewal',
                  style: TextStyles.body1b.copyWith(
                    color: AppColors.primaryTextColor,
                  ),
                ),
                const SizedBox(height: Insets.s),
                Text(
                  'Automatically renew your subscription at the end of each billing period',
                  style: TextStyles.body2.copyWith(
                    color: AppColors.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: checkoutState.autoRenew,
            onChanged: checkoutNotifier.setAutoRenew,
            activeColor: AppColors.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCodeSection(
      SubscriptionCheckoutState checkoutState, SubscriptionCheckoutNotifier checkoutNotifier) {
    return Container(
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
        border: Border.all(
          color: AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Promo Code',
            style: TextStyles.body1b.copyWith(
              color: AppColors.primaryTextColor,
            ),
          ),
          const SizedBox(height: Insets.m),
          TextFormField(
            controller: _promoCodeController,
            decoration: InputDecoration(
              hintText: 'Enter promo code',
              hintStyle: TextStyles.body2.copyWith(
                color: AppColors.hintTextColor,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                borderSide: BorderSide(color: AppColors.inputBorderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMd),
                borderSide: BorderSide(color: AppColors.primaryColor),
              ),
              filled: true,
              fillColor: AppColors.inputBackgroundColor,
              suffixIcon: IconButton(
                icon: Icon(Icons.check, color: AppColors.primaryColor),
                onPressed: () {
                  checkoutNotifier.setPromoCode(_promoCodeController.text.trim());
                },
              ),
            ),
            onChanged: (value) {
              if (value.isEmpty) {
                checkoutNotifier.setPromoCode(null);
              }
            },
          ),
        ],
      ),
    );
  }

  Future<void> _handleSubscribe(SubscriptionCheckoutNotifier checkoutNotifier) async {
    // Clear any previous errors
    checkoutNotifier.clearError();

    // Apply promo code if entered
    final promoCode = _promoCodeController.text.trim();
    if (promoCode.isNotEmpty) {
      checkoutNotifier.setPromoCode(promoCode);
    }

    // Attempt purchase
    final success = await checkoutNotifier.purchaseSubscription();

    if (success) {
      // Navigate to confirmation screen
      NavigationManager.instance.push(SubscriptionConfirmationView.name);
    }
  }
}
