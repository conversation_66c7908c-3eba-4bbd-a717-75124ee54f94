import 'subscription.dart';

/// Purchase subscription data model
class PurchaseSubscriptionData {
  final Subscription subscription;
  final PaymentDetails payment;
  final String welcomeMessage;

  PurchaseSubscriptionData({
    required this.subscription,
    required this.payment,
    required this.welcomeMessage,
  });

  factory PurchaseSubscriptionData.fromJson(Map<String, dynamic> json) {
    return PurchaseSubscriptionData(
      subscription: Subscription.fromJson(json['subscription']),
      payment: PaymentDetails.fromJson(json['payment']),
      welcomeMessage: json['welcome_message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscription': subscription.toJson(),
      'payment': payment.toJson(),
      'welcome_message': welcomeMessage,
    };
  }
}

/// Payment details model
class PaymentDetails {
  final double amount;
  final String currency;
  final String paymentMethod;
  final String status;
  final String? transactionId;
  final DateTime? processedAt;

  PaymentDetails({
    required this.amount,
    required this.currency,
    required this.paymentMethod,
    required this.status,
    this.transactionId,
    this.processedAt,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    return PaymentDetails(
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] ?? 'SAR',
      paymentMethod: json['payment_method'],
      status: json['status'],
      transactionId: json['transaction_id'],
      processedAt: json['processed_at'] != null ? DateTime.parse(json['processed_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency,
      'payment_method': paymentMethod,
      'status': status,
      'transaction_id': transactionId,
      'processed_at': processedAt?.toIso8601String(),
    };
  }

  /// Check if payment was successful
  bool get isCompleted => status == 'completed';
  bool get isPending => status == 'pending';
  bool get isFailed => status == 'failed';

  /// Get formatted amount with currency
  String get formattedAmount => '$amount $currency';
}
