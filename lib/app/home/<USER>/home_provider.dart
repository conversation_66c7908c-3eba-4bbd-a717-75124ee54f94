import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';
import 'package:techrar_gym/app/home/<USER>/home_service.dart';
import 'package:techrar_gym/app/auth/models/customer.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription.dart';

part 'home_provider.g.dart';

@riverpod
class HomeNotifier extends _$HomeNotifier {
  @override
  HomeState build() {
    // Initialize with loading state and trigger data loading
    Future.microtask(() => loadHomeData());
    return const HomeState(isLoading: true);
  }

  Future<void> loadHomeData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = ref.read(homeServiceProvider);

      // Load all data concurrently
      final results = await Future.wait([
        service.getQuickActions(),
        service.getRecentActivities(),
        service.getUpcomingClasses(),
        service.getUserStats(),
        service.getUserProfile(),
        service.getTodaysGoal(),
        service.getMembershipStatus(),
        service.getUserBadges(),
      ]);

      final quickActionsResult = results[0] as dynamic;
      final recentActivitiesResult = results[1] as dynamic;
      final upcomingClassesResult = results[2] as dynamic;
      final userStatsResult = results[3] as dynamic;
      final userProfileResult = results[4] as dynamic;
      final todaysGoalResult = results[5] as dynamic;
      final membershipResult = results[6] as dynamic;
      final userBadgesResult = results[7] as dynamic;

      // Check for errors
      String? error;
      List<QuickAction> quickActions = [];
      List<RecentActivity> recentActivities = [];
      List<UpcomingClass> upcomingClasses = [];
      UserStats? userStats;
      Customer? userProfile;
      TodaysGoal? todaysGoal;
      Subscription? membership;
      UserBadges? userBadges;

      quickActionsResult.fold(
        (err) => error ??= err,
        (data) => quickActions = data,
      );

      recentActivitiesResult.fold(
        (err) => error ??= err,
        (data) => recentActivities = data,
      );

      upcomingClassesResult.fold(
        (err) => error ??= err,
        (data) => upcomingClasses = data,
      );

      userStatsResult.fold(
        (err) => error ??= err,
        (data) => userStats = data,
      );

      userProfileResult.fold(
        (err) => error ??= err,
        (data) => userProfile = data,
      );

      todaysGoalResult.fold(
        (err) => error ??= err,
        (data) => todaysGoal = data,
      );

      membershipResult.fold(
        (err) => error ??= err,
        (data) => membership = data,
      );

      userBadgesResult.fold(
        (err) => error ??= err,
        (data) => userBadges = data,
      );

      state = state.copyWith(
        isLoading: false,
        error: error,
        quickActions: quickActions,
        recentActivities: recentActivities,
        upcomingClasses: upcomingClasses,
        userStats: userStats,
        userProfile: userProfile,
        todaysGoal: todaysGoal,
        membership: membership,
        userBadges: userBadges,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load home data: ${e.toString()}',
      );
    }
  }

  Future<void> refresh() async {
    await loadHomeData();
  }
}
