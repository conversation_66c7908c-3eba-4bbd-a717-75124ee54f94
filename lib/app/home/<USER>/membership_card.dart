import 'package:flutter/material.dart';
import 'package:navigation_utils/navigation_utils.dart';
import 'package:techrar_gym/app/subscriptions/models/subscription.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import 'package:techrar_gym/app/home/<USER>/home_state.dart';
import 'package:techrar_gym/app/subscriptions/views/subscription_plans_view.dart';

class MembershipCard extends StatelessWidget {
  final Subscription? membership;
  final bool isLoading;

  const MembershipCard({
    super.key,
    this.membership,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingCard();
    }

    final isActive = membership?.isActive ?? false;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        color: AppColors.cardColor,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
        border: Border.all(
          color: isActive ? AppColors.successColor.withValues(alpha: 0.3) : AppColors.inputBorderColor,
          width: 1,
        ),
      ),
      child: isActive ? _buildActiveMembership() : _buildNoMembership(),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Insets.l),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        boxShadow: Styles.unifiedShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 20,
            width: 150,
            decoration: BoxDecoration(
              color: AppColors.inputBorderColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: Insets.m),
          Container(
            height: 16,
            width: 100,
            decoration: BoxDecoration(
              color: AppColors.inputBorderColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: Insets.s),
          Container(
            height: 14,
            width: 200,
            decoration: BoxDecoration(
              color: AppColors.inputBorderColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveMembership() {
    final expiryDate = membership?.expiryDate;
    final planName = membership?.planName ?? 'Premium';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Membership Status',
              style: TextStyles.body1b.copyWith(
                color: AppColors.primaryTextColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: Insets.m,
                vertical: Insets.s,
              ),
              decoration: BoxDecoration(
                color: AppColors.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusMd),
              ),
              child: Text(
                'Active',
                style: TextStyles.caption.copyWith(
                  color: AppColors.successColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: Insets.m),
        Text(
          planName,
          style: TextStyles.body1.copyWith(
            color: AppColors.primaryTextColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: Insets.s),
        if (expiryDate != null)
          Text(
            'Expires on ${_formatDate(expiryDate)}',
            style: TextStyles.caption.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          )
        else
          Text(
            'Active membership',
            style: TextStyles.caption.copyWith(
              color: AppColors.secondaryTextColor,
            ),
          ),
        const SizedBox(height: Insets.l),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // TODO: Navigate to membership management
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.buttonTextColor,
              padding: const EdgeInsets.symmetric(vertical: Insets.m),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusMd),
              ),
            ),
            child: Text(
              'Manage',
              style: TextStyles.buttonText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoMembership() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'No Membership',
          style: TextStyles.body1b.copyWith(
            color: AppColors.primaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.s),
        Text(
          'Subscribe to access all gym features and classes',
          style: TextStyles.body2.copyWith(
            color: AppColors.secondaryTextColor,
          ),
        ),
        const SizedBox(height: Insets.l),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              NavigationManager.instance.push(SubscriptionPlansView.name);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.buttonTextColor,
              padding: const EdgeInsets.symmetric(vertical: Insets.m),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSizes.buttonRadius),
              ),
            ),
            child: Text(
              'Subscribe',
              style: TextStyles.buttonText,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
